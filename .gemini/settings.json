{"mcpServers": {"Playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"]}, "Exa Search": {"command": "npx", "args": ["-y", "exa-mcp-server"], "env": {"EXA_API_KEY": "b226d29a-dc05-4977-bdda-5bc55d7328d4"}}, "firecrawl-mcp": {"command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-a491e4cc8f3d4213808806c70a94449e"}, "alwaysAllow": ["firecrawl_search", "firecrawl_scrape"]}, "TikHub.io API Docs": {"command": "npx", "args": ["-y", "apifox-mcp-server@latest", "--site-id=4705614"]}, "飞书 API - API 文档": {"command": "npx", "args": ["-y", "apifox-mcp-server@latest", "--site-id=532425"]}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}, "alwaysAllow": []}, "Context 7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "alwaysAllow": ["resolve-library-id"]}, "Brave Search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAXComZb3HInv5jJ7htVVZ6aZczcli"}}, "promptx": {"command": "npx", "args": ["-y", "-f", "--registry", "https://registry.npmjs.org", "dpml-prompt@dev", "mcp-server"], "alwaysAllow": ["promptx_learn", "promptx_remember", "promptx_recall", "promptx_think", "promptx_init", "promptx_action", "promptx_welcome"]}, "mcp-deepwiki": {"command": "npx", "args": ["-y", "mcp-deep<PERSON><PERSON>@latest"]}, "tavily-remote-mcp": {"command": "npx", "args": ["-y", "mcp-remote", "https://mcp.tavily.com/mcp/?tavilyApiKey=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"], "alwaysAllow": []}, "markitdown-mcp": {"command": "/Users/<USER>/Downloads/Ming-Digital-Garden/venv/bin/markitdown-mcp", "args": [], "alwaysAllow": []}}}